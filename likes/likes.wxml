<!--pages/fold3/likes/likes.wxml-->
<custom-nav-bar title="收到的赞"></custom-nav-bar>

<view class="container">
  <view class="like-list">
    <block wx:if="{{loading}}">
      <view class="loading-container">
        <view class="loading-dots"></view>
      </view>
    </block>
    <block wx:elif="{{likeList.length > 0}}">
      <view class="like-item {{item.highlighted ? 'highlighted' : ''}}"
            wx:for="{{likeList}}"
            wx:key="id"
            bindtap="viewOriginalContent"
            data-id="{{item.message_id}}"
            data-type="{{item.target_type}}"
            data-target_type="{{item.target_type}}"
            data-target_id="{{item.target_id}}"
            data-comment_id="{{item.comment_id}}">
        <view class="user-avatar">
          <image class="avatar" src="{{item.from_user_avatar || '/images/weixiao.png'}}" mode="aspectFill"/>
        </view>
        <view class="message-content">
          <view class="user-name">{{item.from_username}}</view>
          <view class="action-line">
            <view class="action-text">
              <block wx:if="{{item.target_type === 'comment'}}">赞了你的评论</block>
              <block wx:elif="{{item.target_type === 'reply'}}">赞了你的回复</block>
              <block wx:elif="{{item.target_type === 'message'}}">赞了你的帖子</block>
              <block wx:elif="{{item.target_type === 'liaoran_comment'}}">赞了你的了然几分评论</block>
              <block wx:elif="{{item.target_type === 'liaoran_reply'}}">赞了你的了然几分回复</block>
              <block wx:elif="{{item.target_type === 'window_comment'}}">赞了你的食堂评论</block>
              <block wx:elif="{{item.target_type === 'window_reply'}}">赞了你的食堂回复</block>
              <block wx:elif="{{item.target_type === 'major_comment'}}">赞了你的专业评论</block>
              <block wx:elif="{{item.target_type === 'major_reply'}}">赞了你的专业回复</block>
              <block wx:else>赞了你的内容</block>
              <text class="time">{{item.created_at}}</text>
            </view>
          </view>
          <view wx:if="{{item.target_type === 'comment' || item.target_type === 'reply' || item.target_type === 'liaoran_comment' || item.target_type === 'liaoran_reply' || item.target_type === 'window_comment' || item.target_type === 'window_reply' || item.target_type === 'major_comment' || item.target_type === 'major_reply'}}" class="comment-content">
            <view class="quote-mark"></view>
            <text class="comment-text">{{item.target_content}}</text>
          </view>
        </view>
        <view class="right-image" wx:if="{{item.content_image}}">
          <image class="content-thumb" src="{{item.content_image}}" mode="aspectFill"/>
        </view>
      </view>
    </block>
    <view wx:else class="empty-tip">
      <image src="/images/aixin.png" mode="aspectFit" class="empty-image"/>
      <text>还没有收到点赞哦~</text>
    </view>
  </view>
</view> 