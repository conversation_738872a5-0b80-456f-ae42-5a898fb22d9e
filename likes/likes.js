Page({
  data: {
    likeList: [],
    page: 1,
    pageSize: 20,
    hasMore: true,
    loading: true
  },

  onLoad(options) {
    // 页面加载时获取点赞列表
    this.getLikeList();
    // 清零未读消息数
    this.clearUnreadCount();

    // 添加WebSocket监听
    const app = getApp();
    if (app.globalData.websocket) {
      app.globalData.websocket.addListener(this.handleWebSocketMessage);
    }
  },

  onUnload() {
    // 页面卸载时移除WebSocket监听
    const app = getApp();
    if (app.globalData.websocket) {
      app.globalData.websocket.removeListener(this.handleWebSocketMessage);
    }
    
    // 确保离开页面时红点状态更新正确
    // 重新计算总未读消息数并更新TabBar
    const totalUnread = 
      parseInt(app.globalData.unreadComments || 0) + 
      parseInt(app.globalData.unreadReplies || 0) +
      parseInt(app.globalData.unread || 0);
      
    // 设置正确的标志状态
    app.globalData.pendingBadgeUpdate = false;
    
    // 更新TabBar状态
    if (totalUnread > 0) {
      wx.setTabBarBadge({
        index: 3,
        text: totalUnread.toString()
      }).catch(() => {});
    } else {
      wx.removeTabBarBadge({
        index: 3
      }).catch(() => {});
    }
  },

  // 处理WebSocket消息
  handleWebSocketMessage(message) {
    if (message.type === 'notification' && message.data && message.data.type === 'like') {
      // 收到新的点赞通知时刷新列表
      this.refreshList();
      
      // 获取me页面实例并更新未读消息数
      const pages = getCurrentPages();
      const mePage = pages.find(page => page.route === 'pages/fold3/me/me');
      if (mePage && typeof mePage.updateUnreadCounts === 'function') {
        mePage.updateUnreadCounts();
      }
    }
  },

  // 刷新列表
  refreshList() {
    this.setData({
      page: 1,
      hasMore: true
    }, () => {
      this.getLikeList();
    });
  },

  // 获取点赞列表
  getLikeList(isLoadMore = false) {
    const app = getApp();
    if (!app.globalData.user_id) return;

    if (!isLoadMore) {
      this.setData({ loading: true });
    }

    // 构造请求参数，确保page和page_size是数字
    const requestData = {
      user_id: app.globalData.user_id,
      type: 'like',
      page: parseInt(this.data.page),
      page_size: parseInt(this.data.pageSize),
      show_all: 1
    };

    wx.request({
      url: `${app.globalData.wangz}/notification/getList`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: requestData,
      success: (res) => {
        // console.log('[响应数据]', res.data);
        if (res.data.error_code === 0) {
          const newList = res.data.data.list || [];
          this.setData({
            likeList: isLoadMore ? [...this.data.likeList, ...newList] : newList,
            hasMore: newList.length === requestData.page_size,
            loading: false
          });
        } else {
          this.setData({ loading: false });
          wx.showToast({
            title: res.data.message || '获取数据失败',
            icon: 'none'
          });
        }
      },
      fail: (error) => {
        console.error('[请求失败]', error);
        this.setData({ loading: false });
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      }
    });
  },

  // 清零未读消息数
  clearUnreadCount() {
    const app = getApp();
    if (!app.globalData.user_id || !app.globalData.isLoggedIn) return;

    // 构造请求参数
    const requestData = {
      user_id: app.globalData.user_id,
      type: 'like'
    };

    wx.request({
      url: `${app.globalData.wangz}/notification/markRead`,
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded'
      },
      data: requestData,
      success: (res) => {
        if (res.data.error_code === 0) {
          // 全局变量点赞通知计数清零
          app.globalData.unread = 0;
          
          // 获取当前页面栈
          const pages = getCurrentPages();
          const mePage = pages.find(page => page.route === 'pages/fold3/me/me');
          
          // 如果me页面存在，直接更新其grid通知
          if (mePage && typeof mePage.updateGridNotifications === 'function') {
            mePage.updateGridNotifications();
          }
          
          // 关键修复：确保TabBar状态也被正确更新
          // 计算总的未读消息数（包括评论和回复）
          const totalUnread = 
            parseInt(app.globalData.unreadComments || 0) + 
            parseInt(app.globalData.unreadReplies || 0);
          
          // 设置pendingBadgeUpdate为false，防止后续自动更新
          app.globalData.pendingBadgeUpdate = false;
          
          // 直接更新TabBar上的Badge
          const currentPage = pages[pages.length - 1];
          const isTabBarPage = currentPage && 
            ['pages/fold1/home/<USER>', 'pages/fold2/xuqiu/xuqiu', 
             'pages/fold3/me/me', 'pages/fold4/gongju/gongju'].includes(currentPage.route);
          
          if (isTabBarPage) {
            if (totalUnread > 0) {
              wx.setTabBarBadge({
                index: 3,
                text: totalUnread.toString()
              }).catch(() => {});
            } else {
              wx.removeTabBarBadge({
                index: 3
              }).catch(() => {});
            }
          }
        }
      },
      fail: (error) => {
        // 错误处理
      }
    });
  },

  // 查看原始内容
  viewOriginalContent(e) {
    const { id, type } = e.currentTarget.dataset;
    const targetType = e.currentTarget.dataset.target_type;
    let url = '';

    if (type === 'message') {
      url = `/packageEmoji/pages/messageDetail/messageDetail?id=${id}`;
    } else if (type === 'comment') {
      url = `/packageEmoji/pages/messageDetail/messageDetail?id=${id}&comment_id=${e.currentTarget.dataset.target_id}`;
    } else if (type === 'reply') {
      url = `/packageEmoji/pages/messageDetail/messageDetail?id=${id}&comment_id=${e.currentTarget.dataset.comment_id}&reply_id=${e.currentTarget.dataset.target_id}`;
    } else if (targetType === 'liaoran_comment') {
      // 了然几分评论点赞
      url = `/pages/liaoran/objects/detail/index?id=${id}&comment_id=${e.currentTarget.dataset.target_id}`;
    } else if (targetType === 'liaoran_reply') {
      // 了然几分回复点赞
      url = `/pages/liaoran/objects/detail/index?id=${id}&reply_id=${e.currentTarget.dataset.target_id}`;
    } else if (targetType === 'liaoran_object') {
      // 了然几分对象点赞
      url = `/pages/liaoran/objects/detail/index?id=${id}`;
    } else if (targetType === 'window_comment') {
      // 食堂评论点赞
      url = `/pages/window/detail/index?id=${id}&comment_id=${e.currentTarget.dataset.target_id}`;
    } else if (targetType === 'window_reply') {
      // 食堂回复点赞
      url = `/pages/window/detail/index?id=${id}&reply_id=${e.currentTarget.dataset.target_id}`;
    } else if (targetType === 'window') {
      // 食堂窗口点赞
      url = `/pages/window/detail/index?id=${id}`;
    } else if (targetType === 'major_comment') {
      // 专业评论点赞
      url = `/pages/major/detail/index?id=${id}&comment_id=${e.currentTarget.dataset.target_id}`;
    } else if (targetType === 'major_reply') {
      // 专业回复点赞
      url = `/pages/major/detail/index?id=${id}&reply_id=${e.currentTarget.dataset.target_id}`;
    }

    if (url) {
      wx.navigateTo({ url });
    }
  },

  onPullDownRefresh() {
    this.refreshList();
    wx.stopPullDownRefresh();
  },

  onReachBottom() {
    // 上拉加载更多
    if (this.data.hasMore) {
      this.setData({
        page: this.data.page + 1
      }, () => {
        this.getLikeList(true);
      });
    }
  }
}) 