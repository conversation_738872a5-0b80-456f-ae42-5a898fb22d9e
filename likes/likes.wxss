/* pages/fold3/likes/likes.wxss */
page {
  background-color: #ffffff;
}

.container {
  padding: 0;
  min-height: 100vh;
  background-color: #ffffff;
}

.like-list {
  width: 100%;
  padding-top: 16rpx;
  background-color: #ffffff;
}

.like-item {
  display: flex;
  background: #ffffff;
  padding: 32rpx 32rpx;
  position: relative;
}

.like-item::after {
  content: '';
  position: absolute;
  left: 32rpx;
  right: 32rpx;
  bottom: 0;
  height: 1px;
  background-color: #eee;
}

.like-item:last-child::after {
  display: none;
}

.user-avatar {
  margin-right: 24rpx;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
}

.message-content {
  flex: 1;
  overflow: hidden;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.action-line {
  display: flex;
  margin-bottom: 12rpx;
}

.action-text {
  font-size: 26rpx;
  color: #666;
  display: flex;
  align-items: center;
}

.time {
  font-size: 24rpx;
  color: #999;
  margin-left: 12rpx;
}

.comment-content {
  display: flex;
  align-items: center;
  margin-top: 12rpx;
}

.quote-mark {
  width: 4rpx;
  height: 1.4em;
  background-color: #ff6b6b;
  margin-right: 12rpx;
  border-radius: 2rpx;
  flex-shrink: 0;
}

.comment-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  max-height: 3.2em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  padding-top: 2rpx;
}

.right-image {
  margin-left: 24rpx;
}

.content-thumb {
  width: 80rpx;
  height: 80rpx;
  border-radius: 8rpx;
}

.empty-tip {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 260rpx 0;
  margin-top: -100rpx;
}

.empty-image {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 20rpx;
}

.empty-tip text {
  color: #999;
  font-size: 28rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  margin-top: 60rpx;
}

.loading-dots {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #999;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 