<!--pages/common/message-page/message-page.wxml-->
<view class="custom-nav-bar" style="padding-top: {{statusBarHeight}}px;">
  <view class="nav-bar-content">
    <view class="left" bindtap="onBack">
      <image src="/images/chexiao.png" mode="aspectFit" class="back-icon"></image>
    </view>
    <view class="center">
      <text class="title">{{config.title}}</text>
    </view>
    <view class="right" bindtap="onMoreClick">
      <image src="/images/gengduo.png" mode="aspectFit" class="more-icon"></image>
    </view>
  </view>
</view>
<view class="nav-bar-holder" style="height: {{statusBarHeight + 44}}px;"></view>

<!-- 隐藏的message-list组件，用于获取数据 -->
<message-list
  id="messageList"
  type="{{config.type}}"
  title="{{config.title}}"
  empty-config="{{config.emptyConfig}}"
  bind:messagechange="onMessageChange"
  style="display: none;">
</message-list>

<!-- 使用通用message-ui组件显示 -->
<message-ui
  messages="{{messages}}"
  loading="{{loading}}"
  loading-more="{{loadingMore}}"
  has-more="{{hasMore}}"
  show-expand-history="{{showExpandHistory}}"
  loading-history="{{loadingHistory}}"
  message-type="{{messageTypeName}}"
  expand-button-text="{{expandButtonText}}"
  empty-config="{{config.emptyConfig}}"
  bind:itemclick="onItemClick"
  bind:loadmore="onLoadMore"
  bind:refresh="onRefresh"
  bind:expandhistory="onExpandHistory">
</message-ui>
