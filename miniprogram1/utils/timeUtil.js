/**
 * 时间处理工具
 * 解决iOS日期格式兼容性问题
 */

/**
 * 格式化时间字符串，兼容iOS
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的时间显示
 */
function formatTime(timeStr) {
  if (!timeStr) return ''

  // 修复iOS日期格式兼容性问题
  // 将 "2025-04-03 01:13:52" 格式转换为 "2025/04/03 01:13:52"
  const formattedTimeStr = timeStr.replace(/-/g, '/')
  
  const time = new Date(formattedTimeStr)
  
  // 检查日期是否有效
  if (isNaN(time.getTime())) {
    console.warn('Invalid date format:', timeStr)
    return timeStr // 如果解析失败，返回原始字符串
  }
  
  const now = new Date()
  const diff = now - time

  const minute = 60 * 1000
  const hour = 60 * minute
  const day = 24 * hour

  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return `${Math.floor(diff / minute)}分钟前`
  } else if (diff < day) {
    return `${Math.floor(diff / hour)}小时前`
  } else if (diff < 7 * day) {
    return `${Math.floor(diff / day)}天前`
  } else {
    // 格式化为 "x月x日"
    const month = time.getMonth() + 1
    const date = time.getDate()
    return `${month.toString().padStart(2, '0')}月${date.toString().padStart(2, '0')}日`
  }
}

/**
 * 将时间字符串转换为iOS兼容格式
 * @param {string} timeStr - 原始时间字符串
 * @returns {string} iOS兼容的时间字符串
 */
function toIOSCompatibleFormat(timeStr) {
  if (!timeStr) return ''
  
  // 将 "2025-04-03 01:13:52" 转换为 "2025/04/03 01:13:52"
  return timeStr.replace(/-/g, '/')
}

/**
 * 安全创建Date对象，兼容iOS
 * @param {string} timeStr - 时间字符串
 * @returns {Date|null} Date对象或null
 */
function createSafeDate(timeStr) {
  if (!timeStr) return null
  
  const formattedTimeStr = toIOSCompatibleFormat(timeStr)
  const date = new Date(formattedTimeStr)
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    console.warn('Invalid date format:', timeStr)
    return null
  }
  
  return date
}

/**
 * 格式化为具体日期时间
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的日期时间
 */
function formatDateTime(timeStr) {
  const date = createSafeDate(timeStr)
  if (!date) return timeStr
  
  const year = date.getFullYear()
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const hour = date.getHours().toString().padStart(2, '0')
  const minute = date.getMinutes().toString().padStart(2, '0')
  
  return `${year}年${month}月${day}日 ${hour}:${minute}`
}

/**
 * 格式化为简短日期
 * @param {string} timeStr - 时间字符串
 * @returns {string} 格式化后的日期
 */
function formatDate(timeStr) {
  const date = createSafeDate(timeStr)
  if (!date) return timeStr
  
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  
  return `${month}月${day}日`
}

module.exports = {
  formatTime,
  toIOSCompatibleFormat,
  createSafeDate,
  formatDateTime,
  formatDate
}
