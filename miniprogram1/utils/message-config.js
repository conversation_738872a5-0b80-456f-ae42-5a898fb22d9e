/**
 * 消息页面配置
 * 统一管理不同类型消息页面的配置
 */

const messageConfigs = {
  // 点赞消息配置
  likes: {
    type: 'likes',
    title: '收到的赞',
    emptyConfig: {
      icon: '/images/aixin.png',
      text: '还没有收到点赞哦~'
    }
  },

  // 评论回复配置
  replies: {
    type: 'replies', 
    title: '评论我的',
    emptyConfig: {
      icon: '/images/liaotianzidonghuifu.png',
      text: '还没有收到评论和回复哦~'
    }
  },

  // 系统通知配置
  notifications: {
    type: 'notifications',
    title: '消息通知',
    emptyConfig: {
      icon: '/images/xiaoxi.png',
      text: '暂无消息通知~'
    }
  },

  // published配置已删除，使用专门的mypub页面
}

/**
 * 获取消息配置
 * @param {String} type 消息类型
 * @returns {Object} 配置对象
 */
function getMessageConfig(type) {
  return messageConfigs[type] || messageConfigs.notifications
}

/**
 * 处理消息点击事件
 * @param {Object} message 消息对象
 * @param {String} type 消息类型
 */
function handleMessageClick(message, type) {
  switch (type) {
    case 'likes':
      handleLikeClick(message)
      break
    case 'replies':
      handleReplyClick(message)
      break
    case 'notifications':
      handleNotificationClick(message)
      break
    // published类型已删除，使用专门的mypub页面
    default:
      console.log('Unknown message type:', type)
  }
}

/**
 * 处理点赞消息点击
 */
function handleLikeClick(message) {
  // 跳转到原始内容
  const url = getContentUrl(message.target_type, message.target_id, message.message_id)
  if (url) {
    wx.navigateTo({ url })
  }
}

/**
 * 处理评论回复消息点击
 */
function handleReplyClick(message) {
  console.log('处理回复消息点击:', message)
  console.log('target_type:', message.target_type)
  console.log('target_id:', message.target_id)
  console.log('message_id:', message.message_id)

  // 根据target_type决定跳转逻辑
  let url = null

  if (message.target_type === 'comment' || message.target_type === 'reply') {
    // 普通帖子的评论和回复，跳转到帖子详情
    url = getContentUrl('message', message.target_id, message.message_id)
  } else if (message.target_type === 'liaoran_object') {
    // 了然几分对象评论，跳转到了然几分对象详情页
    url = `/pages/liaoran/objects/detail/index?id=${message.message_id}`
  } else if (message.target_type === 'liaoran_comment') {
    // 了然几分评论回复，跳转到了然几分对象详情页并定位到评论
    url = `/pages/liaoran/objects/detail/index?id=${message.message_id}&comment_id=${message.target_id}`
  } else if (message.target_type === 'liaoran_reply') {
    // 了然几分回复的回复，跳转到了然几分对象详情页并定位到回复
    url = `/pages/liaoran/objects/detail/index?id=${message.message_id}&reply_id=${message.target_id}`
  } else {
    // 其他类型（专业、食堂等），根据target_type跳转
    url = getContentUrl(message.target_type, message.target_id, message.message_id)
  }

  console.log('构建的URL:', url)

  if (url) {
    wx.navigateTo({ url })
  } else {
    console.error('无法构建URL，target_type:', message.target_type, 'message_id:', message.message_id)
    wx.showToast({
      title: '无法跳转到对应页面',
      icon: 'none'
    })
  }
}

/**
 * 处理系统通知点击
 */
function handleNotificationClick(message) {
  // 根据通知类型处理
  switch (message.target_type) {
    case 'student_auth':
      // 跳转到认证管理页面（管理员）
      wx.navigateTo({
        url: '/pages/admin/auth-management/auth-management'
      })
      break
    case 'auth_result':
      // 显示认证结果详情
      wx.showModal({
        title: '认证结果',
        content: message.message,
        showCancel: false
      })
      break
    default:
      // 默认显示通知内容
      if (message.message) {
        wx.showModal({
          title: message.title || '系统通知',
          content: message.message,
          showCancel: false
        })
      }
  }
}

// handlePublishedClick函数已删除，使用专门的mypub页面

/**
 * 获取内容URL
 */
function getContentUrl(targetType, targetId, messageId) {
  const urlMap = {
    message: `/packageEmoji/pages/messageDetail/messageDetail?id=${messageId}`,
    comment: `/packageEmoji/pages/messageDetail/messageDetail?id=${messageId}`,
    reply: `/packageEmoji/pages/messageDetail/messageDetail?id=${messageId}`,
    liaoran_comment: `/pages/liaoran/objects/detail/index?id=${messageId}`,
    liaoran_reply: `/pages/liaoran/objects/detail/index?id=${messageId}`,
    liaoran_object: `/pages/liaoran/objects/detail/index?id=${messageId}`,
    window_comment: `/pages/fold3/map/map`,
    window_reply: `/pages/fold3/map/map`,
    major_comment: `/pages/foldshare/xueke/xueke`,
    major_reply: `/pages/foldshare/xueke/xueke`
  }

  return urlMap[targetType]
}

module.exports = {
  messageConfigs,
  getMessageConfig,
  handleMessageClick
}
