<?php
declare (strict_types = 1);

namespace app\controller;

use think\Request;
use think\facade\Db;
use app\BaseController;
use app\util\JwtUtil;

class Notification extends BaseController
{
    /**
     * 格式化时间
     * @param string $datetime 时间字符串
     * @return string 格式化后的时间
     */
    private function formatTime($datetime)
    {
        $timestamp = strtotime($datetime);
        $now = time();
        $diff = $now - $timestamp;
        
        // 小于1小时，显示xx分钟前
        if ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes > 0 ? "{$minutes}分钟前" : "刚刚";
        }
        
        // 今天的消息，显示今天xx:xx
        if (date('Y-m-d', $timestamp) == date('Y-m-d')) {
            return '今天' . date('H:i', $timestamp);
        }
        
        // 昨天的消息，显示昨天xx:xx
        if (date('Y-m-d', $timestamp) == date('Y-m-d', strtotime('-1 day'))) {
            return '昨天' . date('H:i', $timestamp);
        }
        
        // 一周内的消息，显示x天前
        if ($diff < 7 * 24 * 3600) {
            $days = floor($diff / (24 * 3600));
            return "{$days}天前";
        }
        
        // 超过一周的消息，显示月-日
        return date('m月d日', $timestamp);
    }

    /**
     * 统一获取消息列表接口
     * 支持新旧两种调用方式，兼容性更好
     * @param Request $request
     * @return \think\Response
     */
    public function getMessages(Request $request)
    {
        try {
            // 获取用户ID - 支持两种方式
            $userId = null;
            $token = $request->header('token');

            if ($token) {
                // 新方式：使用token验证
                $payload = JwtUtil::validateToken($token);
                if (!$payload) {
                    return json(['code' => 401, 'msg' => 'token无效或已过期']);
                }
                $userId = $payload['user_id'];
            } else {
                // 旧方式：使用user_id参数（兼容旧接口）
                $userId = $request->param('user_id');
                if (empty($userId)) {
                    return json(['code' => 401, 'msg' => '请先登录']);
                }
            }

            $type = $request->param('type', 'notifications');
            $page = (int)$request->param('page', 1);
            $pageSize = (int)$request->param('page_size', 20);
            $showAll = (bool)$request->param('show_all', false);

            // 统一处理所有类型的消息
            return $this->getNotificationData($userId, $type, $page, $pageSize, $showAll);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 获取点赞列表（兼容接口）
     * @param Request $request
     * @return \think\Response
     */
    public function getLikes(Request $request)
    {
        // 设置type参数并调用统一接口
        $request->param(['type' => 'likes']);
        return $this->getMessages($request);
    }

    /**
     * 获取评论回复列表（兼容接口）
     * @param Request $request
     * @return \think\Response
     */
    public function getReplies(Request $request)
    {
        // 设置type参数并调用统一接口
        $request->param(['type' => 'replies']);
        return $this->getMessages($request);
    }

    /**
     * 获取通知列表（兼容旧接口）
     */
    public function getList(Request $request)
    {
        // 调用统一接口，保持旧的返回格式
        $response = $this->getMessages($request);
        $data = json_decode($response->getContent(), true);

        // 转换为旧格式
        if ($data['code'] == 200) {
            return json([
                'error_code' => 0,
                'message' => 'success',
                'data' => [
                    'list' => $data['data'],
                    'total' => $data['total'] ?? count($data['data']),
                    'has_more' => $data['has_more'] ?? false
                ]
            ]);
        } else {
            return json([
                'error_code' => 1,
                'message' => $data['msg'] ?? '获取失败'
            ]);
        }
    }

    /**
     * 标记通知为已读
     */
    public function markRead(Request $request)
    {
        $user_id = $request->param('user_id');
        $type = $request->param('type', 'like');

        if (empty($user_id)) {
            return json([
                'error_code' => 1,
                'message' => '参数错误'
            ]);
        }

        Db::startTrans();
        try {
            // 更新通知状态为已读
            Db::name('notification')
                ->where('user_id', $user_id)
                ->where('type', $type)
                ->where('is_read', 0)
                ->update(['is_read' => 1]);

            // 更新用户未读消息数
            $unread_count = Db::name('notification')
                ->where('user_id', $user_id)
                ->where('is_read', 0)
                ->count();

            Db::name('user')
                ->where('id', $user_id)
                ->update(['unread' => $unread_count]);

            // 如果用户在线，通过WebSocket推送最新未读数
            try {
                $wsServer = app()->make('websocket.server');
                if ($wsServer && isset($wsServer->userConnections[$user_id])) {
                    $connection = $wsServer->userConnections[$user_id];
                    $wsServer->sendMessage($connection, [
                        'type' => 'unread_count',
                        'count' => $unread_count
                    ]);
                }
            } catch (\Exception $e) {
                error_log("WebSocket通知发送失败: " . $e->getMessage());
            }

            Db::commit();

            return json([
                'error_code' => 0,
                'message' => 'success',
                'data' => [
                    'unread_count' => $unread_count
                ]
            ]);

        } catch (\Exception $e) {
            Db::rollback();
            return json([
                'error_code' => 1,
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取系统通知列表（兼容接口）
     */
    public function getNotifications(Request $request)
    {
        // 设置type参数并调用统一接口
        $request->param(['type' => 'notifications']);
        return $this->getMessages($request);
    }

    /**
     * 标记消息为已读
     */
    public function markAsRead(Request $request)
    {
        // 验证token
        $token = $request->header('token');
        if (!$token) {
            return json(['code' => 401, 'msg' => '请先登录']);
        }

        // 验证JWT token
        try {
            $payload = JwtUtil::validateToken($token);
            if (!$payload) {
                return json(['code' => 401, 'msg' => 'token无效或已过期']);
            }
            $userId = $payload['user_id'];
        } catch (\Exception $e) {
            return json(['code' => 401, 'msg' => 'token验证失败']);
        }

        try {
            $type = $request->param('type', 'notifications');
            $ids = $request->param('ids', []);

            // 根据类型标记已读
            $query = Db::table('notification')
                ->where('user_id', $userId);

            switch ($type) {
                case 'likes':
                    $query->where('type', 'like');
                    break;
                case 'replies':
                    $query->whereIn('type', ['comment', 'reply']);
                    break;
                case 'notifications':
                    $query->whereIn('type', ['system', 'student_auth', 'auth_result']);
                    break;
                default:
                    // 如果指定了具体的ID，只标记这些ID
                    if (!empty($ids)) {
                        $query->whereIn('id', $ids);
                    } else {
                        $query->whereIn('type', ['system', 'student_auth', 'auth_result']);
                    }
            }

            $query->where('is_read', 0)
                  ->update(['is_read' => 1, 'updated_at' => date('Y-m-d H:i:s')]);

            return json(['code' => 200, 'msg' => '标记成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '标记失败：' . $e->getMessage()]);
        }
    }

    /**
     * 发送通知给管理员
     */
    public function sendToAdmins($type, $title, $message, $relatedId = null, $icon = null)
    {
        try {
            // 获取所有管理员用户
            $admins = Db::table('user')
                ->where('status', '管理员')
                ->column('id');

            if (empty($admins)) {
                return false;
            }

            // 为每个管理员创建通知（使用现有notification表结构）
            $notifications = [];
            foreach ($admins as $adminId) {
                $notifications[] = [
                    'user_id' => $adminId,
                    'from_user_id' => 0, // 系统通知
                    'type' => $type,
                    'target_type' => $type,
                    'target_id' => $relatedId ?: 0,
                    'message_id' => 0,
                    'content' => $title,
                    'target_content' => $message,
                    'content_image' => $icon,
                    'is_read' => 0,
                    'created_at' => date('Y-m-d H:i:s')
                ];
            }

            // 批量插入通知
            Db::name('notification')->insertAll($notifications);

            return true;

        } catch (\Exception $e) {
            error_log('发送通知失败：' . $e->getMessage());
            return false;
        }
    }



    /**
     * 标记所有消息为已读
     * @param Request $request
     * @return \think\Response
     */
    public function markAllAsRead(Request $request)
    {
        try {
            // 验证token
            $jwtUtil = new \app\utils\JwtUtil();
            $tokenData = $jwtUtil->verifyToken($request->header('token'));
            if (!$tokenData) {
                return json(['code' => 401, 'msg' => 'Token验证失败']);
            }

            $userId = $tokenData['user_id'];
            $type = $request->param('type');

            // 根据类型标记所有消息为已读
            $query = Db::table('notification')
                ->where('to_user_id', $userId)
                ->where('is_read', 0);

            switch ($type) {
                case 'likes':
                    $query->where('type', 'like');
                    break;
                case 'replies':
                    $query->whereIn('type', ['comment', 'reply']);
                    break;
                case 'notifications':
                    $query->whereIn('type', ['system', 'student_auth', 'auth_result']);
                    break;
                default:
                    // 标记所有未读消息
                    break;
            }

            $query->update(['is_read' => 1, 'updated_at' => date('Y-m-d H:i:s')]);

            return json(['code' => 200, 'msg' => '全部标记成功']);

        } catch (\Exception $e) {
            return json(['code' => 500, 'msg' => '服务器错误: ' . $e->getMessage()]);
        }
    }

    /**
     * 根据target_type获取对应内容的图片
     */
    private function getTargetImage($type, $target_id, $message_images)
    {
        try {
            switch ($type) {
                case 'message':
                    // 帖子的图片直接从message_images获取
                    if (!empty($message_images)) {
                        $images = json_decode($message_images, true);
                        if (is_array($images) && !empty($images)) {
                            return $images[0]; // 取第一张图片
                        }
                    }
                    break;

                case 'comment':
                    // 评论的图片需要从comment表获取
                    $comment = Db::name('comment')
                        ->where('id', $target_id)
                        ->field('images')
                        ->find();
                    if ($comment && !empty($comment['images'])) {
                        $images = json_decode($comment['images'], true);
                        if (is_array($images) && !empty($images)) {
                            return $images[0];
                        }
                    }
                    break;

                case 'reply':
                    // 回复的图片需要从post表获取
                    $reply = Db::name('post')
                        ->where('id', $target_id)
                        ->field('images')
                        ->find();
                    if ($reply && !empty($reply['images'])) {
                        $images = json_decode($reply['images'], true);
                        if (is_array($images) && !empty($images)) {
                            return $images[0];
                        }
                    }
                    break;

                default:
                    // 其他类型暂时返回null
                    break;
            }
        } catch (\Exception $e) {
            // 出错时返回null
        }

        return null;
    }

    /**
     * 统一的通知数据获取方法
     * @param int $userId 用户ID
     * @param string $type 消息类型：like, comment, reply, notifications
     * @param int $page 页码
     * @param int $pageSize 每页数量
     * @param bool $showAll 是否显示所有（包括已读）
     * @return \think\Response
     */
    private function getNotificationData($userId, $type, $page, $pageSize, $showAll)
    {
        try {
            // 根据类型确定查询条件
            $typeConditions = $this->getTypeConditions($type);
            if (!$typeConditions) {
                return json(['code' => 400, 'msg' => '无效的消息类型']);
            }

            // 系统通知使用简化查询
            if ($type === 'notifications') {
                return $this->getSystemNotifications($userId, $page, $pageSize, $showAll);
            }

            // 构建查询
            $query = Db::name('notification')
                ->alias('n')
                ->join('user u', 'n.from_user_id = u.id')
                ->leftJoin('message m', 'n.target_type = "message" AND n.target_id = m.id')
                ->leftJoin('comment c', 'n.target_type = "comment" AND n.target_id = c.id')
                ->leftJoin('message m2', 'n.target_type = "comment" AND c.message_id = m2.id')
                ->leftJoin('post p', 'n.target_type = "reply" AND n.target_id = p.id')
                ->leftJoin('message m3', 'n.target_type = "reply" AND p.message_id = m3.id')
                ->leftJoin('major_comments mc', '(n.target_type = "major_comment" OR n.target_type = "major_reply") AND n.target_id = mc.id')
                ->leftJoin('buaa_majors bm', '(n.target_type = "major_comment" OR n.target_type = "major_reply") AND n.message_id = bm.id')
                ->where('n.user_id', $userId)
                ->where('n.from_user_id', '<>', $userId);

            // 应用类型条件
            if (is_array($typeConditions)) {
                $query->whereIn('n.type', $typeConditions);
            } else {
                $query->where('n.type', $typeConditions);
            }

            // 如果不显示所有通知，只显示未读
            if (!$showAll) {
                $query->where('n.is_read', 0);
            }

            // 获取总数
            $total = $query->count();

            // 获取列表数据
            $list = $query->field([
                'n.id',
                'n.user_id',
                'n.from_user_id',
                'u.username as from_username',
                'u.face_url as from_user_avatar',
                'n.type',
                'n.target_type',
                'n.target_id',
                'n.message_id',
                'CASE
                    WHEN n.target_type = "reply" THEN p.parent_comment_id
                    WHEN n.target_type = "major_reply" THEN mc.parent_id
                    ELSE NULL
                END as comment_id',
                'n.content',
                'n.target_content',
                'n.content_image',
                'n.created_at',
                'n.is_read',
                'CASE
                    WHEN n.target_type = "message" THEN m.is_anonymous
                    WHEN n.target_type = "comment" THEN m2.is_anonymous
                    WHEN n.target_type = "reply" THEN m3.is_anonymous
                    WHEN n.target_type = "major_comment" OR n.target_type = "major_reply" THEN 0
                    ELSE 0
                END as message_is_anonymous',
                'CASE
                    WHEN n.target_type = "message" THEN m.images
                    WHEN n.target_type = "comment" THEN c.images
                    WHEN n.target_type = "reply" THEN p.images
                    ELSE NULL
                END as target_images'
            ])
            ->order('n.created_at', 'desc')
            ->page($page, $pageSize)
            ->select()
            ->each(function($item) {
                return $this->formatNotificationItem($item);
            })
            ->toArray();

            // 计算是否还有更多
            $hasMore = ($page * $pageSize) < $total;

            // 返回统一格式 - 兼容新旧接口
            return json([
                'code' => 200,
                'error_code' => 0,
                'msg' => '获取成功',
                'message' => 'success',
                'data' => $list,
                // 兼容旧格式
                'list' => $list,
                'total' => $total,
                'has_more' => $hasMore
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'error_code' => 1,
                'msg' => '服务器错误: ' . $e->getMessage(),
                'message' => $e->getMessage()
            ]);
        }
    }

    /**
     * 获取类型查询条件
     */
    private function getTypeConditions($type)
    {
        switch ($type) {
            case 'like':
            case 'likes':
                return 'like';
            case 'comment':
                return 'comment';
            case 'reply':
                return 'reply';
            case 'replies':
                return ['comment', 'reply'];
            case 'notifications':
                return ['system', 'student_auth', 'auth_result'];
            default:
                return false;
        }
    }

    /**
     * 格式化通知项目
     */
    private function formatNotificationItem($item)
    {
        // 格式化时间
        $item['created_at'] = $this->formatTime($item['created_at']);

        // 确保所有必要字段都存在
        $item['comment_id'] = $item['comment_id'] ?? null;
        $item['target_content'] = $item['target_content'] ?? '';
        $item['content_image'] = $item['content_image'] ?? '';

        // 处理图片
        if (!empty($item['target_images'])) {
            $images = json_decode($item['target_images'], true);
            if (is_array($images) && !empty($images)) {
                $item['content_image'] = $images[0];
            }
        }
        unset($item['target_images']);

        // 处理匿名通知显示
        if (isset($item['message_is_anonymous']) && $item['message_is_anonymous'] == 1) {
            $anonymousUtil = new \app\utils\AnonymousUtil();
            $anonymousName = $anonymousUtil::generateAnonymousName($item['from_user_id'], $item['message_id']);
            $anonymousAvatar = $anonymousUtil::generateAnonymousAvatar($item['from_user_id'], $item['message_id']);

            // 替换用户信息为匿名信息
            $item['from_username'] = $anonymousName;
            $item['from_user_avatar'] = $anonymousAvatar;
            $item['is_anonymous_notification'] = true;

            // 清理敏感信息
            unset($item['from_user_id']);
        } else {
            $item['is_anonymous_notification'] = false;
            $item['from_user_avatar'] = $item['from_user_avatar'] ?: '/images/weixiao.png';
        }

        // 清理不需要返回给前端的字段
        unset($item['message_is_anonymous']);

        return $item;
    }

    /**
     * 获取系统通知
     */
    private function getSystemNotifications($userId, $page, $pageSize, $showAll)
    {
        try {
            $query = Db::name('notification')
                ->where('user_id', $userId)
                ->whereIn('type', ['system', 'student_auth', 'auth_result']);

            // 如果不显示所有通知，只显示未读
            if (!$showAll) {
                $query->where('is_read', 0);
            }

            // 获取总数
            $total = $query->count();

            // 获取列表数据
            $list = $query->field('id, content, target_content, content_image, is_read, created_at, target_type, target_id, type')
                ->order('created_at', 'desc')
                ->page($page, $pageSize)
                ->select()
                ->each(function($item) {
                    // 格式化时间
                    $item['created_at'] = $this->formatTime($item['created_at']);

                    // 格式化为统一格式
                    return [
                        'id' => $item['id'],
                        'from_user_id' => 0, // 系统通知
                        'from_username' => '系统通知',
                        'from_user_avatar' => $item['content_image'] ?: '/images/xiaoxi.png',
                        'target_type' => $item['type'],
                        'target_id' => $item['target_id'],
                        'target_content' => $item['target_content'] ?: '',
                        'message_id' => 0,
                        'content_image' => $item['content_image'] ?: '/images/xiaoxi.png',
                        'created_at' => $item['created_at'],
                        'is_read' => $item['is_read'] == 1,
                        'content' => $item['content'],
                        'type' => $item['type'],
                        'is_anonymous_notification' => false
                    ];
                })
                ->toArray();

            // 计算是否还有更多
            $hasMore = ($page * $pageSize) < $total;

            return json([
                'code' => 200,
                'error_code' => 0,
                'msg' => '获取成功',
                'message' => 'success',
                'data' => $list,
                'total' => $total,
                'has_more' => $hasMore
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'error_code' => 1,
                'msg' => '获取通知数据失败: ' . $e->getMessage(),
                'message' => $e->getMessage()
            ]);
        }
    }


}