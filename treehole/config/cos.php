<?php
return [
    // 基础配置
    'region' => 'ap-beijing',
    'credentials' => [
        'secretId' => 'AKIDhGbllaEhx54qRpdIjy0oOo6FF3f3D9rM',     // 替换为你的SecretId
        'secretKey' => 'KTQCl6UgQzZ1GRSHxRhnhOs8B2g6II9p',   // 替换为你的SecretKey
    ],

    // 存储桶配置
    'buckets' => [
        // 公有存储桶（普通图片）
        'public' => [
            'bucket' => 'treeholepublic-1320255796',  // 你的公有存储桶名称
            'domains' => [
                'default' => 'https://treeholepublic-1320255796.cos.ap-beijing.myqcloud.com', // 开发环境直连COS
                'production' => 'https://public.bjgaoxiaoshequ.store', // EdgeOne加速域名
            ],
            'acl' => 'public-read',
            'acceleration' => 'edgeone' // 标记使用EdgeOne加速
        ],

        // 私有存储桶（敏感文件）
        'private' => [
            'bucket' => 'treehole-1320255796', // 你的私有存储桶名称
            'domains' => [
                'default' => 'https://treehole-1320255796.cos.ap-beijing.myqcloud.com', // 开发环境直连COS
                'production' => 'https://private.bjgaoxiaoshequ.store', // EdgeOne加速域名
            ],
            'acl' => 'private',
            'acceleration' => 'edgeone' // 标记使用EdgeOne加速
        ]
    ],

    // 文件类型映射
    'file_mapping' => [
        // 公有存储桶文件类型
        'public_types' => ['comment', 'life', 'group', 'canteen', 'activity', 'liaoran', 'common', 'avatar'], // 添加avatar到公有存储桶
        // 私有存储桶文件类型
        'private_types' => ['auth', 'schedule', 'touxiang', 'renzheng', 'kebiao'] // 移除avatar
    ],

    // 本地开发配置
    'local_config' => [
        'enable_cos' => false, // 临时禁用COS
        'fallback_to_local' => true, // 失败时是否回退到本地存储
    ]
];
