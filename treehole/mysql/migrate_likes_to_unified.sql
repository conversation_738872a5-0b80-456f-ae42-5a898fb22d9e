-- ========================================
-- 点赞数据迁移脚本
-- 将现有的分散点赞表数据迁移到统一点赞表
-- ========================================

-- 1. 先创建统一点赞表（如果不存在）
CREATE TABLE IF NOT EXISTS `unified_likes` (
  `id` int NOT NULL AUTO_INCREMENT,
  `user_id` int NOT NULL COMMENT '用户ID',
  `target_type` varchar(20) NOT NULL COMMENT '目标类型：message/comment/reply/liaoran_comment/liaoran_reply',
  `target_id` int NOT NULL COMMENT '目标ID',
  `message_id` int DEFAULT NULL COMMENT '关联帖子ID（用于通知）',
  `comment_id` int DEFAULT NULL COMMENT '关联评论ID（回复点赞时使用）',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_message` (`message_id`),
  KEY `idx_comment` (`comment_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='统一点赞表';

-- 2. 迁移帖子点赞数据
INSERT IGNORE INTO `unified_likes` (`user_id`, `target_type`, `target_id`, `message_id`, `comment_id`, `created_at`)
SELECT 
    `user_id`,
    'message' as `target_type`,
    `message_id` as `target_id`,
    `message_id`,
    NULL as `comment_id`,
    `created_at`
FROM `message_like`
WHERE EXISTS (SELECT 1 FROM `message_like`);

-- 3. 迁移评论点赞数据
INSERT IGNORE INTO `unified_likes` (`user_id`, `target_type`, `target_id`, `message_id`, `comment_id`, `created_at`)
SELECT 
    `user_id`,
    'comment' as `target_type`,
    `comment_id` as `target_id`,
    `message_id`,
    `comment_id`,
    `created_at`
FROM `comment_like`
WHERE EXISTS (SELECT 1 FROM `comment_like`);

-- 4. 迁移回复点赞数据
INSERT IGNORE INTO `unified_likes` (`user_id`, `target_type`, `target_id`, `message_id`, `comment_id`, `created_at`)
SELECT 
    `user_id`,
    'reply' as `target_type`,
    `reply_id` as `target_id`,
    `message_id`,
    `comment_id`,
    `created_at`
FROM `reply_like`
WHERE EXISTS (SELECT 1 FROM `reply_like`);

-- 5. 迁移了然几分评论点赞数据
INSERT IGNORE INTO `unified_likes` (`user_id`, `target_type`, `target_id`, `message_id`, `comment_id`, `created_at`)
SELECT 
    `user_id`,
    'liaoran_comment' as `target_type`,
    `comment_id` as `target_id`,
    NULL as `message_id`,
    NULL as `comment_id`,
    `created_at`
FROM `liaoran_comment_likes`
WHERE EXISTS (SELECT 1 FROM `liaoran_comment_likes`);

-- 6. 迁移comment_likes表数据（如果存在）
INSERT IGNORE INTO `unified_likes` (`user_id`, `target_type`, `target_id`, `message_id`, `comment_id`, `created_at`)
SELECT 
    `user_id`,
    'comment' as `target_type`,
    `comment_id` as `target_id`,
    NULL as `message_id`,
    `comment_id`,
    `created_at`
FROM `comment_likes`
WHERE EXISTS (SELECT 1 FROM `comment_likes`);

-- ========================================
-- 验证迁移结果
-- ========================================

-- 查看迁移后的数据统计
SELECT 
    target_type,
    COUNT(*) as total_likes,
    COUNT(DISTINCT user_id) as unique_users,
    COUNT(DISTINCT target_id) as unique_targets
FROM unified_likes 
GROUP BY target_type
ORDER BY target_type;

-- 验证帖子点赞数据
SELECT 
    '帖子点赞' as type,
    (SELECT COUNT(*) FROM message_like) as original_count,
    (SELECT COUNT(*) FROM unified_likes WHERE target_type = 'message') as migrated_count;

-- 验证评论点赞数据
SELECT 
    '评论点赞' as type,
    (SELECT COUNT(*) FROM comment_like) as original_count,
    (SELECT COUNT(*) FROM unified_likes WHERE target_type = 'comment') as migrated_count;

-- 验证回复点赞数据
SELECT 
    '回复点赞' as type,
    (SELECT COUNT(*) FROM reply_like) as original_count,
    (SELECT COUNT(*) FROM unified_likes WHERE target_type = 'reply') as migrated_count;

-- 验证了然几分评论点赞数据
SELECT 
    '了然几分评论点赞' as type,
    (SELECT COUNT(*) FROM liaoran_comment_likes) as original_count,
    (SELECT COUNT(*) FROM unified_likes WHERE target_type = 'liaoran_comment') as migrated_count;

-- ========================================
-- 可选：备份原表后删除（谨慎操作）
-- ========================================

-- 注意：以下操作会删除原始点赞表，请确保数据迁移成功后再执行

-- 重命名原表为备份表
-- RENAME TABLE `message_like` TO `message_like_backup`;
-- RENAME TABLE `comment_like` TO `comment_like_backup`;
-- RENAME TABLE `reply_like` TO `reply_like_backup`;
-- RENAME TABLE `liaoran_comment_likes` TO `liaoran_comment_likes_backup`;
-- RENAME TABLE `comment_likes` TO `comment_likes_backup`;


